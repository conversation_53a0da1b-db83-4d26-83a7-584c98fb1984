# orchestrator.py

import asyncio
from mcp_agent.app import <PERSON><PERSON><PERSON>
from mcp_agent.workflows.orchestrator.orchestrator import Orchestrator
from mcp_agent.workflows.llm.augmented_llm_openai import OpenAIAugmentedLLM
from agents import get_fetch_agent, get_filesystem_agent, get_gmail_agent, get_rag_agent, get_activecampaign_agent, get_sbca_agent, get_sage_intacct_agent

async def run_orchestrator():
    app = MCPApp(name="ai_workspace_orchestrator")

    async with app.run() as mcp_agent_app:
        logger = mcp_agent_app.logger

        orchestrator = Orchestrator(
            llm_factory=OpenAIAugmentedLLM,
            available_agents=[
                get_activecampaign_agent(),
                get_sbca_agent(),
                get_sage_intacct_agent()                
            ]
        )

        # Example user query
        user_question = "Find my latest report, summarize any related articles, and gather related emails."

        result = await orchestrator.generate_str(user_question)

        logger.info(f"Final result: {result}")

if __name__ == "__main__":
    asyncio.run(run_orchestrator())
