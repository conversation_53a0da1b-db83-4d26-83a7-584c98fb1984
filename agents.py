# agents.py

from mcp_agent.agents.agent import Agent

def get_activecampaign_agent():
    return Agent(
        name="activecampaign_agent",
        instruction=(
            "You can manage marketing contacts, campaigns, and automations "
            "via the ActiveCampaign system."
        ),
        server_names=["activecampaign"]
    )

def get_sbca_agent():
    return Agent(
        name="sbca_agent",
        instruction=(
            "You can query accounting and business data using the SBCA (Sage Business Cloud Accounting) system."
        ),
        server_names=["sbca"]
    )

def get_sage_intacct_agent():
    return Agent(
        name="sage_intacct_agent",
        instruction=(
            "You can retrieve and update accounting data using the Sage Intacct system."
        ),
        server_names=["sage-intacct"]  # Match the key used in mcp_agent.config.yaml
    )

def get_fetch_agent():
    return Agent(
        name="fetch_agent",
        instruction="You can fetch content from the web.",
        server_names=["fetch"]
    )

def get_filesystem_agent():
    return Agent(
        name="filesystem_agent",
        instruction="You can read and write files on the local filesystem.",
        server_names=["filesystem"]
    )

def get_gmail_agent():
    return Agent(
        name="gmail_agent",
        instruction="You can read and send emails.",
        server_names=["gmail"]
    )

def get_rag_agent():
    return Agent(
        name="rag_agent",
        instruction="You can search documents using vector search.",
        server_names=["qdrant"]
    )
