mcp:
  servers:    
    activecampaign:
      command: "uvx"
      args:
        - ac-mcp-server
      env:
        AC_API_TOKEN: "<your_token_here>"
        AC_API_URL: "<your_api_url_here>"
      
    sbca:
      command: "npx"
      args:
        - -y
        - supergateway
        - --sse
        - https://sbca-mcp.provenlabs.xyz/sse

    # sage-intacct:
    #   command: "uvx"
    #   args:
    #     - mcp-client
    #     - --transport
    #     - sse
    #     - --url
    #     - http://localhost:8001/sse
    #     - --header
    #     - '{"Authorization":"Bearer <token>"}'
    
    fetch:
      command: "uvx"
      args: ["mcp-server-fetch"]

    filesystem:
      command: "npx"
      args: ["@modelcontextprotocol/server-filesystem", "<add_your_dirs>"]

    gmail:
      command: "uvx"
      args: ["mcp-server-gmail"]

    qdrant:
      command: "uvx"
      args: ["mcp-server-qdrant"]


logger:
  transports: [console]
  level: info
